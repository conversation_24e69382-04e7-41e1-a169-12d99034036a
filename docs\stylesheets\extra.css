/* 自定义样式 */

/* 链接样式优化 */
a {
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-bottom-color 0.2s;
}

a:hover {
  border-bottom-color: var(--md-accent-fg-color);
}

/* 博客文章列表样式 */
.md-content article {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--md-default-fg-color--lightest);
}

.md-content article:last-child {
  border-bottom: none;
}

/* 文章元信息样式 */
.post-meta {
  color: var(--md-default-fg-color--light);
  margin-bottom: 0.5rem;
}

.post-meta .post-date {
  margin-right: 1rem;
}

.post-meta .post-category {
  margin-right: 0.5rem;
}

.post-meta .post-tags a {
  background: var(--md-accent-fg-color--transparent);
  color: var(--md-accent-fg-color);
  padding: 0.2rem 0.5rem;
  border-radius: 0.2rem;
  margin-right: 0.3rem;
  text-decoration: none;
}

/* 精选文章卡片样式 */
.featured-posts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.featured-post {
  background: var(--md-default-bg-color);
  border: 1px solid var(--md-default-fg-color--lightest);
  border-radius: 0.5rem;
  padding: 1.5rem;
  transition: box-shadow 0.2s, transform 0.2s;
}

.featured-post:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.featured-post h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

.featured-post .post-excerpt {
  color: var(--md-default-fg-color--light);
  margin-bottom: 1rem;
}

.featured-post .read-more {
  color: var(--md-accent-fg-color);
  font-weight: 500;
  text-decoration: none;
}

/* 导航卡片样式 */
.nav-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
}

.nav-card {
  background: var(--md-default-bg-color);
  border: 1px solid var(--md-default-fg-color--lightest);
  border-radius: 0.5rem;
  padding: 1.5rem;
  text-align: center;
  transition: box-shadow 0.2s, transform 0.2s;
  text-decoration: none;
  color: inherit;
}

.nav-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
  text-decoration: none;
  color: inherit;
}

.nav-card .icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--md-accent-fg-color);
}

.nav-card h3 {
  margin: 0 0 0.5rem 0;
}

.nav-card p {
  margin: 0;
  color: var(--md-default-fg-color--light);
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .featured-posts {
    grid-template-columns: 1fr;
  }

  .nav-cards {
    grid-template-columns: 1fr;
  }
}

/* 目录样式优化 */
.md-nav--secondary .md-nav__title {
  font-weight: 600;
  color: var(--md-default-fg-color);
}

/* 搜索结果优化 */
.md-search-result__meta {
  color: var(--md-default-fg-color--light);
}

/* 标签页面样式 */
.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 1rem 0;
}

.tag-cloud a {
  background: var(--md-accent-fg-color--transparent);
  color: var(--md-accent-fg-color);
  padding: 0.3rem 0.8rem;
  border-radius: 1rem;
  text-decoration: none;
  transition: background-color 0.2s;
}

.tag-cloud a:hover {
  background: var(--md-accent-fg-color);
  color: var(--md-primary-bg-color);
}



{"config": {"lang": ["zh"], "separator": "[\\s\\u200b\\u3000\\-、。，．？！；]+", "pipeline": ["stemmer"]}, "docs": [{"location": "", "title": "欢迎来到 NeXtep's Blog", "text": "<p>这里是我分享技术学习、思考感悟和生活记录的地方。专注于机器学习、深度学习、编程技术以及个人成长。</p>"}, {"location": "#_1", "title": "导航", "text": "📝 博客 <p>技术文章与学习笔记</p> 💻 代码 <p>常用代码片段与工具</p> 🏷️ 标签 <p>按主题分类浏览</p> 🛠️ 工具 <p>实用工具与资源</p> 👋 关于 <p>了解更多关于我</p>"}, {"location": "#_2", "title": "精选内容", "text": "线性代数基础 机器学习数学基础              机器学习中的线性代数基础知识，包括向量、矩阵运算、特征值分解等核心概念。          继续阅读 → 深度学习基础 深度学习              深度学习的基础概念、神经网络原理以及常用框架的使用方法。          继续阅读 → 前沿论文 学术研究              最新的机器学习和人工智能领域论文解读与分析。          继续阅读 →"}, {"location": "#_3", "title": "最新更新", "text": "<p>查看 博客页面 获取最新的文章和更新内容。</p> <p>持续学习，持续分享。欢迎通过 GitHub 与我交流。</p>"}, {"location": "about/", "title": "关于", "text": ""}, {"location": "blog/", "title": "博客", "text": "📂 导航 ▶           📅 归档          <ul> <li> ▶                 2025年                <ul> <li>1月 3篇</li> </ul> </li> <li> ▶                 2024年                <ul> <li>12月 0篇</li> </ul> </li> </ul> ▶           🏷️ 标签          <ul> <li>机器学习 2篇</li> <li>深度学习 2篇</li> <li>Python 2篇</li> <li>PyTorch 1篇</li> <li>入门 2篇</li> <li>教程 2篇</li> <li>理论 1篇</li> <li>算法 1篇</li> </ul> ▶           📁 分类          <ul> <li>机器学习 2篇</li> <li>深度学习 2篇</li> <li>神经网络 1篇</li> </ul>      # 所有文章      这里是我的技术博客，记录学习过程中的思考和总结。      ## 最新文章      ### 机器学习数学基础系列      - [线性代数基础](math4ml/linear_algebra.md) - 机器学习中的线性代数核心概念     - [概率论基础](math4ml/probability_theory.md) - 概率论在机器学习中的应用     - [统计学基础](math4ml/statistics.md) - 统计学习理论基础      ### 深度学习系列      - [深度学习基础](deepl/basic.md) - 神经网络原理与实践      ### 前沿论文解读      - [论文阅读笔记](papers/index.md) - 最新研究成果分析      ---      *持续更新中，欢迎交流讨论。*"}, {"location": "blog/deepl/basic/", "title": "transformer 基础", "text": "<p>概要：简要记录 Encoder-Decoder 架构、seq2seq 模型、Attention 机制</p>"}, {"location": "blog/deepl/basic/#encoder-decoder", "title": "Encoder &amp; Decoder", "text": "<p>encoder 接收输入，生成一个固定长度的上下文向量（编码器生成的最终隐藏状态）；decoder 接收上下文向量（或状态）+输入，获得输出。</p> <p></p> <p>缺点：上下文长度固定，导致丢失信息。</p>"}, {"location": "blog/deepl/basic/#_1", "title": "代码实现", "text": "<pre><code>form torch import nn\n\nclass Encoder(nn.<PERSON><PERSON><PERSON>):\n    \"\"\"编码器接口\"\"\"\n    def __init__(self, **kwargs):\n        super(Encoder, self).__init__(**kwargs)\n    def forward(self, x, *args):\n        raise NotImplementedError\n\nclass Decoder(nn.<PERSON><PERSON><PERSON>):\n    \"\"\"解码器接口\"\"\"\n    def __init__(self, **kwargs):\n        super(Decoder, self).__init__(**kwargs)\n    def init_state(self, enc_outputs, *args):\n        raise NotImplementedError\n    def forward(self, x, *args):\n        raise NotImplementedError\n\nclass EncoderDecoder(nn.Module):\n    \"\"\"编码器-解码器接口\"\"\"\n    def __init__(self, encoder, decoder, **kwargs):\n        super(EncoderDecoder, self).__init__(**kwargs)\n        self.encoder = encoder\n        self.decoder = decoder\n    def forward(self, enc_X, dec_X, *args):\n        enc_outputs = self.encoder(enc_X, *args)\n        dec_state = self.decoder.init_state(enc_outputs, *args)\n        return self.decoder(dec_X, dec_state)\n</code></pre>"}, {"location": "blog/deepl/basic/#seq2seq", "title": "seq2seq", "text": "<p>seq2seq 是一种使用 encoder-decoder 框架解决可变长度序列生成序列问题的模型。</p> <p>在 seq2seq 模型提出前，解决序列生成序列问题，只能输入和输出均为固定长度的序列。如果输入和输出序列的长度不足，那么需要 padding + mask 来处理。</p> <p>问题：</p> <ol> <li>seq2seq 模型提出前，输入输出序列要求为固定长度，意思是需要预处理输入序列长度为已定义的固定长度吗？</li> <li>seq2seq 模型可以处理可变长度序列，意思是输入序列可以是任意长度吗？如果 decoder 或 encoder 每个时间步只能处理序列中的一个单词，那么总时间步的步数需要预先定义吗？</li> <li>为什么 seq2seq 存在短期记忆限制？seq2seq 里面的 rnn 模块只处理序列中的当前元素信息吗？也会有上一时间步的隐藏状态信息吧？但是没有更长时间前的信息了</li> <li>encoder 会输出一个最终隐藏状态，decoder 中每个 rnn 模块接收的隐藏状态都一样吗？还是说 decoder 中的每个 rnn 模块会在上一个的基础上增加一些信息？</li> </ol>"}, {"location": "blog/deepl/basic/#_2", "title": "训练和推理", "text": "<p>训练过程：在 decoder 中，每个时间步都输入正确的单词</p> <p>推理过程：在 decoder 中，每个时间步的输入使用上一时间步的预测输出</p>"}, {"location": "blog/deepl/basic/#attention", "title": "Attention 机制", "text": "<p>不再要求 encoder 基于整个序列编码出一个固定长度的上下文向量，而是编码出一个上下文向量序列，解决信息丢失和短期记忆限制的问题。</p> <p></p> <p>参考：</p> <p>动手学深度学习-李沐-9.6. 编码器-解码器架构</p> <p>动手学深度学习-李沐-9.7. 序列到序列学习（seq2seq）</p> <p>动手学深度学习-李沐-seq2seq-b站视频</p> <p>fun transformer-Datawhale-引言</p>"}, {"location": "blog/papers/", "title": "Attention is all your need", "text": "<p>新增前沿论文</p>"}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/", "title": "深度学习基础：从感知机到神经网络", "text": "<p>深度学习是机器学习的一个子领域，它使用多层神经网络来学习数据的复杂表示。</p>", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#_2", "title": "神经网络的历史", "text": "<p>神经网络的发展经历了几个重要阶段：</p> <ol> <li>1943年: McCulloch-Pitts 神经元模型</li> <li>1957年: Rosenblatt 提出感知机</li> <li>1986年: 反向传播算法的普及</li> <li>2006年: 深度学习的复兴</li> <li>2012年: AlexNet 在 ImageNet 上的突破</li> </ol>", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#_3", "title": "感知机模型", "text": "<p>感知机是最简单的神经网络模型：</p> \\[ y = f(\\sum_{i=1}^{n} w_i x_i + b) \\] <p>其中： - \\(x_i\\) 是输入特征 - \\(w_i\\) 是权重 - \\(b\\) 是偏置 - \\(f\\) 是激活函数</p> <pre><code>import numpy as np\n\nclass Perceptron:\n    def __init__(self, learning_rate=0.01, n_iterations=1000):\n        self.learning_rate = learning_rate\n        self.n_iterations = n_iterations\n\n    def fit(self, X, y):\n        # 初始化权重和偏置\n        self.weights = np.zeros(X.shape[1])\n        self.bias = 0\n\n        # 训练过程\n        for _ in range(self.n_iterations):\n            for i in range(X.shape[0]):\n                # 前向传播\n                linear_output = np.dot(X[i], self.weights) + self.bias\n                prediction = self.activation(linear_output)\n\n                # 更新权重\n                update = self.learning_rate * (y[i] - prediction)\n                self.weights += update * X[i]\n                self.bias += update\n\n    def activation(self, x):\n        return 1 if x &gt;= 0 else 0\n\n    def predict(self, X):\n        linear_output = np.dot(X, self.weights) + self.bias\n        return [self.activation(x) for x in linear_output]\n</code></pre>", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#mlp", "title": "多层感知机 (MLP)", "text": "<p>多层感知机通过添加隐藏层来解决非线性问题：</p> <pre><code>import torch\nimport torch.nn as nn\nimport torch.optim as optim\n\nclass MLP(nn.Module):\n    def __init__(self, input_size, hidden_size, output_size):\n        super(MLP, self).__init__()\n        self.hidden = nn.Linear(input_size, hidden_size)\n        self.output = nn.Linear(hidden_size, output_size)\n        self.relu = nn.ReLU()\n        self.sigmoid = nn.Sigmoid()\n\n    def forward(self, x):\n        x = self.relu(self.hidden(x))\n        x = self.sigmoid(self.output(x))\n        return x\n\n# 使用示例\nmodel = MLP(input_size=784, hidden_size=128, output_size=10)\ncriterion = nn.CrossEntropyLoss()\noptimizer = optim.Adam(model.parameters(), lr=0.001)\n</code></pre>", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#_4", "title": "激活函数", "text": "<p>激活函数为神经网络引入非线性：</p>", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#1-sigmoid", "title": "1. <PERSON><PERSON><PERSON><PERSON>", "text": "\\[ \\sigma(x) = \\frac{1}{1 + e^{-x}} \\]", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#2-relu-rectified-linear-unit", "title": "2. ReLU (Rectified Linear Unit)", "text": "\\[ \\text{ReLU}(x) = \\max(0, x) \\]", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#3-tanh", "title": "3. <PERSON><PERSON>", "text": "\\[ \\tanh(x) = \\frac{e^x - e^{-x}}{e^x + e^{-x}} \\]", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#_5", "title": "反向传播算法", "text": "<p>反向传播是训练神经网络的核心算法：</p> <ol> <li>前向传播: 计算输出和损失</li> <li>反向传播: 计算梯度</li> <li>参数更新: 使用梯度下降更新权重</li> </ol> <pre><code># 简化的反向传播示例\ndef backward_propagation(X, y, weights, learning_rate):\n    m = X.shape[0]\n\n    # 前向传播\n    z = np.dot(X, weights)\n    a = sigmoid(z)\n\n    # 计算损失\n    cost = -np.mean(y * np.log(a) + (1 - y) * np.log(1 - a))\n\n    # 反向传播\n    dz = a - y\n    dw = (1/m) * np.dot(X.T, dz)\n\n    # 更新权重\n    weights = weights - learning_rate * dw\n\n    return weights, cost\n</code></pre>", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#_6", "title": "深度学习的优势", "text": "<ol> <li>自动特征学习: 无需手工设计特征</li> <li>处理复杂数据: 图像、文本、语音等</li> <li>端到端学习: 从原始数据到最终输出</li> <li>可扩展性: 能够处理大规模数据</li> </ol>", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#_7", "title": "常见的深度学习架构", "text": "", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#1-cnn", "title": "1. 卷积神经网络 (CNN)", "text": "<ul> <li>主要用于图像处理</li> <li>包含卷积层、池化层、全连接层</li> </ul>", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#2-rnn", "title": "2. 循环神经网络 (RNN)", "text": "<ul> <li>处理序列数据</li> <li>LSTM、GRU 等变体</li> </ul>", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#3-transformer", "title": "3. Transformer", "text": "<ul> <li>注意力机制</li> <li>在 NLP 领域取得突破</li> </ul>", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#_8", "title": "训练技巧", "text": "", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#1", "title": "1. 权重初始化", "text": "<pre><code># Xavier 初始化\nnn.init.xavier_uniform_(layer.weight)\n\n# He 初始化\nnn.init.kaiming_uniform_(layer.weight)\n</code></pre>", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#2", "title": "2. 批量归一化", "text": "<pre><code>self.bn = nn.BatchNorm1d(hidden_size)\n</code></pre>", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#3-dropout", "title": "3. Dropout", "text": "<pre><code>self.dropout = nn.Dropout(0.5)\n</code></pre>", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#_9", "title": "常见问题和解决方案", "text": "", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#_10", "title": "梯度消失/爆炸", "text": "<ul> <li>使用 ReLU 激活函数</li> <li>批量归一化</li> <li>残差连接</li> </ul>", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#_11", "title": "过拟合", "text": "<ul> <li>Dropout</li> <li>正则化</li> <li>数据增强</li> </ul>", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#_12", "title": "实践建议", "text": "<ol> <li>从简单开始: 先理解基础概念</li> <li>动手实践: 实现简单的神经网络</li> <li>使用框架: PyTorch、TensorFlow</li> <li>参与项目: Kaggle 竞赛、开源项目</li> </ol>", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/25/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E4%BB%8E%E6%84%9F%E7%9F%A5%E6%9C%BA%E5%88%B0%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/#_13", "title": "总结", "text": "<p>深度学习虽然概念复杂，但核心思想是通过多层非线性变换来学习数据的表示。理解基础概念，结合实践，是掌握深度学习的关键。</p> <p>下一步可以学习具体的网络架构和应用领域！</p>", "tags": ["PyTorch", "TensorFlow", "神经网络"]}, {"location": "blog/2025/01/15/%E6%88%91%E7%9A%84%E7%AC%AC%E4%B8%80%E7%AF%87%E5%8D%9A%E5%AE%A2%E6%96%87%E7%AB%A0/", "title": "我的第一篇博客文章", "text": "<p>欢迎来到我的技术博客！这是第一篇文章，用来测试博客功能。</p>", "tags": ["入门", "教程"]}, {"location": "blog/2025/01/15/%E6%88%91%E7%9A%84%E7%AC%AC%E4%B8%80%E7%AF%87%E5%8D%9A%E5%AE%A2%E6%96%87%E7%AB%A0/#_2", "title": "关于这个博客", "text": "<p>这个博客主要分享：</p> <ul> <li>机器学习和深度学习的学习笔记</li> <li>Python 编程技巧和最佳实践</li> <li>前沿论文的解读和分析</li> <li>个人项目的开发经验</li> </ul>", "tags": ["入门", "教程"]}, {"location": "blog/2025/01/15/%E6%88%91%E7%9A%84%E7%AC%AC%E4%B8%80%E7%AF%87%E5%8D%9A%E5%AE%A2%E6%96%87%E7%AB%A0/#_3", "title": "技术栈", "text": "<p>目前使用的技术栈包括：</p> <ul> <li>文档生成: Material for MkDocs</li> <li>部署: GitHub Pages</li> <li>版本控制: Git</li> </ul>", "tags": ["入门", "教程"]}, {"location": "blog/2025/01/15/%E6%88%91%E7%9A%84%E7%AC%AC%E4%B8%80%E7%AF%87%E5%8D%9A%E5%AE%A2%E6%96%87%E7%AB%A0/#_4", "title": "示例代码", "text": "<p>这里是一个简单的 Python 示例：</p> <pre><code>import numpy as np\nimport matplotlib.pyplot as plt\n\n# 生成示例数据\nx = np.linspace(0, 10, 100)\ny = np.sin(x)\n\n# 绘制图形\nplt.figure(figsize=(10, 6))\nplt.plot(x, y, 'b-', linewidth=2, label='sin(x)')\nplt.xlabel('x')\nplt.ylabel('y')\nplt.title('正弦函数图像')\nplt.legend()\nplt.grid(True)\nplt.show()\n</code></pre>", "tags": ["入门", "教程"]}, {"location": "blog/2025/01/15/%E6%88%91%E7%9A%84%E7%AC%AC%E4%B8%80%E7%AF%87%E5%8D%9A%E5%AE%A2%E6%96%87%E7%AB%A0/#_5", "title": "数学公式", "text": "<p>支持 LaTeX 数学公式：</p> \\[ f(x) = \\frac{1}{\\sqrt{2\\pi\\sigma^2}} e^{-\\frac{(x-\\mu)^2}{2\\sigma^2}} \\] <p>这是正态分布的概率密度函数。</p>", "tags": ["入门", "教程"]}, {"location": "blog/2025/01/15/%E6%88%91%E7%9A%84%E7%AC%AC%E4%B8%80%E7%AF%87%E5%8D%9A%E5%AE%A2%E6%96%87%E7%AB%A0/#_6", "title": "总结", "text": "<p>希望这个博客能够记录我的学习历程，也希望能对其他学习者有所帮助。</p> <p>欢迎通过 GitHub 与我交流讨论！</p>", "tags": ["入门", "教程"]}, {"location": "blog/2025/01/20/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%85%A5%E9%97%A8%E6%8C%87%E5%8D%97/", "title": "机器学习入门指南", "text": "<p>机器学习是人工智能的一个重要分支，它让计算机能够从数据中学习并做出预测或决策。</p>", "tags": ["算法", "理论", "入门"]}, {"location": "blog/2025/01/20/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%85%A5%E9%97%A8%E6%8C%87%E5%8D%97/#_2", "title": "什么是机器学习？", "text": "<p>机器学习是一种让计算机系统能够自动学习和改进的方法，无需明确编程。它基于算法，这些算法可以从数据中学习模式，并使用这些模式对新数据进行预测。</p>", "tags": ["算法", "理论", "入门"]}, {"location": "blog/2025/01/20/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%85%A5%E9%97%A8%E6%8C%87%E5%8D%97/#_3", "title": "机器学习的类型", "text": "", "tags": ["算法", "理论", "入门"]}, {"location": "blog/2025/01/20/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%85%A5%E9%97%A8%E6%8C%87%E5%8D%97/#1-supervised-learning", "title": "1. 监督学习 (Supervised Learning)", "text": "<p>监督学习使用标记的训练数据来学习输入和输出之间的映射关系。</p> <p>常见算法： - 线性回归 - 逻辑回归 - 决策树 - 随机森林 - 支持向量机 (SVM)</p>", "tags": ["算法", "理论", "入门"]}, {"location": "blog/2025/01/20/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%85%A5%E9%97%A8%E6%8C%87%E5%8D%97/#2-unsupervised-learning", "title": "2. 无监督学习 (Unsupervised Learning)", "text": "<p>无监督学习从未标记的数据中发现隐藏的模式。</p> <p>常见算法： - K-means 聚类 - 层次聚类 - 主成分分析 (PCA) - 关联规则学习</p>", "tags": ["算法", "理论", "入门"]}, {"location": "blog/2025/01/20/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%85%A5%E9%97%A8%E6%8C%87%E5%8D%97/#3-reinforcement-learning", "title": "3. 强化学习 (Reinforcement Learning)", "text": "<p>强化学习通过与环境交互来学习最优行为策略。</p>", "tags": ["算法", "理论", "入门"]}, {"location": "blog/2025/01/20/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%85%A5%E9%97%A8%E6%8C%87%E5%8D%97/#_4", "title": "机器学习工作流程", "text": "<pre><code># 典型的机器学习工作流程\nimport pandas as pd\nfrom sklearn.model_selection import train_test_split\nfrom sklearn.linear_model import LinearRegression\nfrom sklearn.metrics import mean_squared_error\n\n# 1. 数据收集和预处理\ndata = pd.read_csv('dataset.csv')\nX = data.drop('target', axis=1)\ny = data['target']\n\n# 2. 数据分割\nX_train, X_test, y_train, y_test = train_test_split(\n    X, y, test_size=0.2, random_state=42\n)\n\n# 3. 模型训练\nmodel = LinearRegression()\nmodel.fit(X_train, y_train)\n\n# 4. 模型评估\npredictions = model.predict(X_test)\nmse = mean_squared_error(y_test, predictions)\nprint(f'均方误差: {mse}')\n</code></pre>", "tags": ["算法", "理论", "入门"]}, {"location": "blog/2025/01/20/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%85%A5%E9%97%A8%E6%8C%87%E5%8D%97/#_5", "title": "关键概念", "text": "", "tags": ["算法", "理论", "入门"]}, {"location": "blog/2025/01/20/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%85%A5%E9%97%A8%E6%8C%87%E5%8D%97/#-", "title": "偏差-方差权衡", "text": "<p>机器学习模型的误差可以分解为：</p> \\[ \\text{总误差} = \\text{偏差}^2 + \\text{方差} + \\text{噪声} \\] <ul> <li>偏差 (Bias): 模型预测值与真实值之间的差异</li> <li>方差 (Variance): 模型对训练数据变化的敏感性</li> <li>噪声 (Noise): 数据中的随机误差</li> </ul>", "tags": ["算法", "理论", "入门"]}, {"location": "blog/2025/01/20/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%85%A5%E9%97%A8%E6%8C%87%E5%8D%97/#_6", "title": "过拟合与欠拟合", "text": "<ul> <li>过拟合: 模型在训练数据上表现很好，但在新数据上表现较差</li> <li>欠拟合: 模型过于简单，无法捕捉数据中的重要模式</li> </ul>", "tags": ["算法", "理论", "入门"]}, {"location": "blog/2025/01/20/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%85%A5%E9%97%A8%E6%8C%87%E5%8D%97/#_7", "title": "评估指标", "text": "", "tags": ["算法", "理论", "入门"]}, {"location": "blog/2025/01/20/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%85%A5%E9%97%A8%E6%8C%87%E5%8D%97/#_8", "title": "回归问题", "text": "<ul> <li>均方误差 (MSE)</li> <li>平均绝对误差 (MAE)</li> <li>R² 分数</li> </ul>", "tags": ["算法", "理论", "入门"]}, {"location": "blog/2025/01/20/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%85%A5%E9%97%A8%E6%8C%87%E5%8D%97/#_9", "title": "分类问题", "text": "<ul> <li>准确率 (Accuracy)</li> <li>精确率 (Precision)</li> <li>召回率 (Recall)</li> <li>F1 分数</li> </ul>", "tags": ["算法", "理论", "入门"]}, {"location": "blog/2025/01/20/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%85%A5%E9%97%A8%E6%8C%87%E5%8D%97/#_10", "title": "学习建议", "text": "<ol> <li>数学基础: 线性代数、概率论、统计学</li> <li>编程技能: Python、R、SQL</li> <li>实践项目: 从简单的数据集开始</li> <li>持续学习: 关注最新研究和技术发展</li> </ol>", "tags": ["算法", "理论", "入门"]}, {"location": "blog/2025/01/20/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%85%A5%E9%97%A8%E6%8C%87%E5%8D%97/#_11", "title": "推荐资源", "text": "<ul> <li>书籍: 《统计学习方法》、《机器学习》(周志华)</li> <li>在线课程: Coursera、edX、Udacity</li> <li>实践平台: Kaggle、Google Colab</li> <li>框架: scikit-learn、TensorFlow、PyTorch</li> </ul>", "tags": ["算法", "理论", "入门"]}, {"location": "blog/2025/01/20/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%85%A5%E9%97%A8%E6%8C%87%E5%8D%97/#_12", "title": "总结", "text": "<p>机器学习是一个快速发展的领域，需要理论知识和实践经验的结合。从基础概念开始，逐步深入到具体算法和应用，是学习机器学习的有效路径。</p> <p>记住：实践是最好的老师，多动手做项目！</p>", "tags": ["算法", "理论", "入门"]}, {"location": "tool/", "title": "效率", "text": ""}, {"location": "tool/#_2", "title": "代码块", "text": ""}, {"location": "tool/#_3", "title": "工具", "text": ""}, {"location": "blog/archive/2025/", "title": "2025", "text": ""}, {"location": "blog/category/%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0/", "title": "深度学习", "text": ""}, {"location": "blog/category/%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/", "title": "神经网络", "text": ""}, {"location": "blog/category/%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0/", "title": "机器学习", "text": ""}, {"location": "blog/category/python/", "title": "Python", "text": ""}, {"location": "tag/", "title": "标签", "text": "<p>通过标签快速找到感兴趣的内容。</p>"}, {"location": "tag/#_2", "title": "技术标签", "text": "机器学习 深度学习 Python PyTorch TensorFlow 算法 数据科学 神经网络"}, {"location": "tag/#_3", "title": "学习标签", "text": "入门 教程 理论 实践 项目 笔记"}, {"location": "tag/#_4", "title": "所有标签", "text": "<p>下面是按字母顺序排列的所有标签：</p>"}, {"location": "tag/#pytorch", "title": "PyTorch", "text": "<ul> <li>深度学习基础：从感知机到神经网络</li> </ul>"}, {"location": "tag/#tensorflow", "title": "TensorFlow", "text": "<ul> <li>深度学习基础：从感知机到神经网络</li> </ul>"}, {"location": "tag/#_5", "title": "入门", "text": "<ul> <li>我的第一篇博客文章</li> <li>机器学习入门指南</li> </ul>"}, {"location": "tag/#_6", "title": "教程", "text": "<ul> <li>我的第一篇博客文章</li> </ul>"}, {"location": "tag/#_7", "title": "理论", "text": "<ul> <li>机器学习入门指南</li> </ul>"}, {"location": "tag/#_8", "title": "神经网络", "text": "<ul> <li>深度学习基础：从感知机到神经网络</li> </ul>"}, {"location": "tag/#_9", "title": "算法", "text": "<ul> <li>机器学习入门指南</li> </ul>"}]}
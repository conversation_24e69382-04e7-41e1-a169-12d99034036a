{% extends "base.html" %}

<!-- 博客页面的自定义布局 -->
{% block content %}
<div class="md-content" data-md-component="content">
  <article class="md-content__inner md-typeset">
    
    <!-- 博客页面标题 -->
    <header class="md-typeset">
      <h1>{{ page.title or "博客" }}</h1>
      {% if page.meta.description %}
        <p class="md-typeset__description">{{ page.meta.description }}</p>
      {% endif %}
    </header>

    <!-- 博客内容 -->
    <div class="blog-layout">
      <!-- 左侧导航 -->
      <aside class="blog-sidebar">
        {% include "partials/nav-blog.html" %}
      </aside>

      <!-- 主要内容区域 -->
      <main class="blog-content">
        {{ page.content }}
        
        <!-- 博客文章列表 -->
        {% if page.children %}
          <div class="blog-posts">
            {% for post in page.children %}
              <article class="blog-post-preview">
                <header class="post-header">
                  <h2><a href="{{ post.url }}">{{ post.title }}</a></h2>
                  <div class="post-meta">
                    {% if post.meta.date %}
                      <span class="post-date">📅 {{ post.meta.date.strftime('%Y年%m月%d日') }}</span>
                    {% endif %}
                    {% if post.meta.categories %}
                      <span class="post-categories">
                        {% for category in post.meta.categories %}
                          <a href="/blog/category/{{ category }}/" class="post-category">{{ category }}</a>
                        {% endfor %}
                      </span>
                    {% endif %}
                    {% if post.meta.tags %}
                      <span class="post-tags">
                        {% for tag in post.meta.tags %}
                          <a href="/blog/tags/#{{ tag }}" class="post-tag">#{{ tag }}</a>
                        {% endfor %}
                      </span>
                    {% endif %}
                  </div>
                </header>
                
                {% if post.meta.description %}
                  <div class="post-excerpt">
                    {{ post.meta.description }}
                  </div>
                {% endif %}
                
                <footer class="post-footer">
                  <a href="{{ post.url }}" class="read-more">继续阅读 →</a>
                </footer>
              </article>
            {% endfor %}
          </div>
        {% endif %}
      </main>

      <!-- 右侧目录 -->
      <aside class="blog-toc">
        {% if page.toc %}
          <nav class="md-nav md-nav--secondary" aria-label="目录">
            <label class="md-nav__title" for="__toc">
              <span class="md-nav__icon md-icon"></span>
              目录
            </label>
            <ul class="md-nav__list">
              {% for toc_item in page.toc %}
                <li class="md-nav__item">
                  <a href="{{ toc_item.url }}" class="md-nav__link">
                    {{ toc_item.title }}
                  </a>
                  {% if toc_item.children %}
                    <ul class="md-nav__list">
                      {% for child in toc_item.children %}
                        <li class="md-nav__item">
                          <a href="{{ child.url }}" class="md-nav__link">
                            {{ child.title }}
                          </a>
                        </li>
                      {% endfor %}
                    </ul>
                  {% endif %}
                </li>
              {% endfor %}
            </ul>
          </nav>
        {% endif %}
      </aside>
    </div>

  </article>
</div>

<style>
/* 博客布局样式 */
.blog-layout {
  display: grid;
  grid-template-columns: 250px 1fr 200px;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.blog-sidebar {
  position: sticky;
  top: 4rem;
  height: fit-content;
  max-height: calc(100vh - 8rem);
  overflow-y: auto;
}

.blog-content {
  min-width: 0; /* 防止内容溢出 */
}

.blog-toc {
  position: sticky;
  top: 4rem;
  height: fit-content;
  max-height: calc(100vh - 8rem);
  overflow-y: auto;
}

/* 博客文章预览样式 */
.blog-posts {
  margin-top: 2rem;
}

.blog-post-preview {
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid var(--md-default-fg-color--lightest);
}

.blog-post-preview:last-child {
  border-bottom: none;
}

.post-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.post-header h2 a {
  color: var(--md-default-fg-color);
  text-decoration: none;
}

.post-header h2 a:hover {
  color: var(--md-accent-fg-color);
}

.post-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: var(--md-default-fg-color--light);
}

.post-category {
  background: var(--md-accent-fg-color--transparent);
  color: var(--md-accent-fg-color);
  padding: 0.2rem 0.5rem;
  border-radius: 0.2rem;
  text-decoration: none;
  font-size: 0.8rem;
}

.post-tag {
  color: var(--md-accent-fg-color);
  text-decoration: none;
  font-size: 0.8rem;
}

.post-tag:hover {
  text-decoration: underline;
}

.post-excerpt {
  color: var(--md-default-fg-color--light);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.read-more {
  color: var(--md-accent-fg-color);
  font-weight: 500;
  text-decoration: none;
}

.read-more:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media screen and (max-width: 1200px) {
  .blog-layout {
    grid-template-columns: 200px 1fr 180px;
    gap: 1.5rem;
  }
}

@media screen and (max-width: 960px) {
  .blog-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .blog-sidebar,
  .blog-toc {
    position: static;
    max-height: none;
  }
  
  .blog-sidebar {
    order: 2;
  }
  
  .blog-content {
    order: 1;
  }
  
  .blog-toc {
    order: 3;
  }
}

@media screen and (max-width: 768px) {
  .post-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
{% endblock %}

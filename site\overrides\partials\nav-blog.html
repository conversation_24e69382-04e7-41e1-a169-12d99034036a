<!-- 博客页面的自定义导航 -->
<nav class="md-nav md-nav--secondary" aria-label="博客导航">
  <label class="md-nav__title" for="__nav_blog">
    <span class="md-nav__icon md-icon"></span>
    博客导航
  </label>
  <input class="md-nav__toggle md-toggle" type="checkbox" id="__nav_blog" checked>
  <ul class="md-nav__list">
    
    <!-- 归档部分 -->
    <li class="md-nav__item md-nav__item--nested">
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__nav_archive" checked>
      <label class="md-nav__link" for="__nav_archive">
        <span class="md-nav__icon md-icon"></span>
        📅 归档
      </label>
      <nav class="md-nav" aria-label="归档">
        <ul class="md-nav__list">
          <li class="md-nav__item md-nav__item--nested">
            <input class="md-nav__toggle md-toggle" type="checkbox" id="__nav_2025">
            <label class="md-nav__link" for="__nav_2025">
              <span class="md-nav__icon md-icon"></span>
              2025年
            </label>
            <nav class="md-nav" aria-label="2025年">
              <ul class="md-nav__list">
                <li class="md-nav__item">
                  <a href="/blog/2025/01/" class="md-nav__link">
                    1月 (3篇)
                  </a>
                </li>
              </ul>
            </nav>
          </li>
          <li class="md-nav__item md-nav__item--nested">
            <input class="md-nav__toggle md-toggle" type="checkbox" id="__nav_2024">
            <label class="md-nav__link" for="__nav_2024">
              <span class="md-nav__icon md-icon"></span>
              2024年
            </label>
            <nav class="md-nav" aria-label="2024年">
              <ul class="md-nav__list">
                <li class="md-nav__item">
                  <a href="/blog/2024/12/" class="md-nav__link">
                    12月 (0篇)
                  </a>
                </li>
              </ul>
            </nav>
          </li>
        </ul>
      </nav>
    </li>

    <!-- 标签部分 -->
    <li class="md-nav__item md-nav__item--nested">
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__nav_tags" checked>
      <label class="md-nav__link" for="__nav_tags">
        <span class="md-nav__icon md-icon"></span>
        🏷️ 标签
      </label>
      <nav class="md-nav" aria-label="标签">
        <ul class="md-nav__list">
          <li class="md-nav__item md-nav__item--nested">
            <input class="md-nav__toggle md-toggle" type="checkbox" id="__nav_tech_tags">
            <label class="md-nav__link" for="__nav_tech_tags">
              <span class="md-nav__icon md-icon"></span>
              技术分类
            </label>
            <nav class="md-nav" aria-label="技术分类">
              <ul class="md-nav__list">
                <li class="md-nav__item">
                  <a href="/blog/tags/#机器学习" class="md-nav__link">
                    机器学习 (2篇)
                  </a>
                </li>
                <li class="md-nav__item">
                  <a href="/blog/tags/#深度学习" class="md-nav__link">
                    深度学习 (2篇)
                  </a>
                </li>
                <li class="md-nav__item">
                  <a href="/blog/tags/#Python" class="md-nav__link">
                    Python (2篇)
                  </a>
                </li>
                <li class="md-nav__item">
                  <a href="/blog/tags/#PyTorch" class="md-nav__link">
                    PyTorch (1篇)
                  </a>
                </li>
                <li class="md-nav__item">
                  <a href="/blog/tags/#神经网络" class="md-nav__link">
                    神经网络 (1篇)
                  </a>
                </li>
              </ul>
            </nav>
          </li>
          <li class="md-nav__item md-nav__item--nested">
            <input class="md-nav__toggle md-toggle" type="checkbox" id="__nav_level_tags">
            <label class="md-nav__link" for="__nav_level_tags">
              <span class="md-nav__icon md-icon"></span>
              难度分类
            </label>
            <nav class="md-nav" aria-label="难度分类">
              <ul class="md-nav__list">
                <li class="md-nav__item">
                  <a href="/blog/tags/#入门" class="md-nav__link">
                    入门 (2篇)
                  </a>
                </li>
                <li class="md-nav__item">
                  <a href="/blog/tags/#教程" class="md-nav__link">
                    教程 (2篇)
                  </a>
                </li>
                <li class="md-nav__item">
                  <a href="/blog/tags/#理论" class="md-nav__link">
                    理论 (1篇)
                  </a>
                </li>
                <li class="md-nav__item">
                  <a href="/blog/tags/#算法" class="md-nav__link">
                    算法 (1篇)
                  </a>
                </li>
              </ul>
            </nav>
          </li>
        </ul>
      </nav>
    </li>

    <!-- 分类部分 -->
    <li class="md-nav__item md-nav__item--nested">
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__nav_categories">
      <label class="md-nav__link" for="__nav_categories">
        <span class="md-nav__icon md-icon"></span>
        📂 分类
      </label>
      <nav class="md-nav" aria-label="分类">
        <ul class="md-nav__list">
          <li class="md-nav__item">
            <a href="/blog/category/机器学习/" class="md-nav__link">
              机器学习 (2篇)
            </a>
          </li>
          <li class="md-nav__item">
            <a href="/blog/category/深度学习/" class="md-nav__link">
              深度学习 (2篇)
            </a>
          </li>
          <li class="md-nav__item">
            <a href="/blog/category/神经网络/" class="md-nav__link">
              神经网络 (1篇)
            </a>
          </li>
        </ul>
      </nav>
    </li>

  </ul>
</nav>

<style>
/* 自定义博客导航样式 */
.md-nav--secondary .md-nav__title {
  font-weight: 600;
  color: var(--md-default-fg-color);
  padding: 0.6rem 1.2rem;
  background: var(--md-default-fg-color--lightest);
  border-radius: 0.2rem;
  margin-bottom: 0.5rem;
}

.md-nav--secondary .md-nav__item--nested > .md-nav__link {
  font-weight: 500;
  color: var(--md-accent-fg-color);
}

.md-nav--secondary .md-nav__link:hover {
  background: var(--md-accent-fg-color--transparent);
}

.md-nav--secondary .md-nav__list .md-nav__list {
  padding-left: 1rem;
}

.md-nav--secondary .md-nav__link {
  font-size: 0.9rem;
  line-height: 1.4;
  padding: 0.3rem 0.8rem;
  border-radius: 0.2rem;
  transition: background-color 0.2s;
}
</style>

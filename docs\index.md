---
title: 主页  # 页面名称
hide:
#    - navigation # 显示右
#    - toc #显示左
#    - footer
#    - feedback  
comments: true  #默认不开启评论
---

# 首页

For full documentation visit [mkdocs.org](https://www.mkdocs.org).

## Commands

* `mkdocs new [dir-name]` - Create a new project.
* `mkdocs serve` - Start the live-reloading docs server.
* `mkdocs build` - Build the documentation site.
* `mkdocs -h` - Print help message and exit.

## Project layout

    mkdocs.yml    # The configuration file.
    docs/
        index.md  # The documentation homepage.
        ...       # Other markdown pages, images and other files.

## code

``` python
import pandas as pd
print("a")
```

``` python title="test.py"
import pandas as pd
print("a")
```

## demo

``` python linenums="1"
print(1)
print(2)
```

``` python hl_lines="1" linenums="1"
print(1)
print(2)
```

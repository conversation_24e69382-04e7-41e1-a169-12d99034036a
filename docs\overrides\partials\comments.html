{% if page.meta.comments %}
<h2 id="__comments">{{ lang.t("meta.comments") }}</h2>
<!-- Insert generated snippet here -->
<script src="https://giscus.app/client.js" data-repo="InTheFuture7/InTheFuture7.github.io" data-repo-id="R_kgDONr6SoA"
    data-category="Announcements" data-category-id="DIC_kwDONr6SoM4CmWd0" data-mapping="pathname" data-strict="0"
    data-reactions-enabled="1" data-emit-metadata="0" data-input-position="bottom" data-theme="preferred_color_scheme"
    data-lang="zh-CN" crossorigin="anonymous" async>
    </script>
<!-- Synchronize Giscus theme with palette -->
<script>
    var giscus = document.querySelector("script[src*=giscus]")

    // Set palette on initial load
    var palette = __md_get("__palette")
    if (palette && typeof palette.color === "object") {
        var theme = palette.color.scheme === "slate"
            ? "transparent_dark"
            : "light"

        // Instruct Giscus to set theme
        giscus.setAttribute("data-theme", theme)
    }

    // Register event handlers after documented loaded
    document.addEventListener("DOMContentLoaded", function () {
        var ref = document.querySelector("[data-md-component=palette]")
        ref.addEventListener("change", function () {
            var palette = __md_get("__palette")
            if (palette && typeof palette.color === "object") {
                var theme = palette.color.scheme === "slate"
                    ? "transparent_dark"
                    : "light"

                // Instruct Giscus to change theme
                var frame = document.querySelector(".giscus-frame")
                frame.contentWindow.postMessage(
                    { giscus: { setConfig: { theme } } },
                    "https://giscus.app"
                )
            }
        })
    })
</script>
{% endif %}
/* 自定义字体和排版样式 */

/* 设置中文字体 */
:root {
  --md-text-font: "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
  --md-code-font: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", "Source Code Pro", monospace;
}

/* 全局字体设置 */
body {
  font-family: var(--md-text-font);
  font-size: 16px;
  line-height: 1.7;
  letter-spacing: 0.02em;
}

/* 标题字体优化 */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--md-text-font);
  font-weight: 600;
  letter-spacing: 0.01em;
}

h1 {
  font-size: 2.2em;
  line-height: 1.3;
}

h2 {
  font-size: 1.8em;
  line-height: 1.4;
}

h3 {
  font-size: 1.5em;
  line-height: 1.4;
}

/* 正文段落优化 */
p {
  margin-bottom: 1.2em;
  text-align: justify;
}

/* 代码块字体 */
code, pre {
  font-family: var(--md-code-font);
}

/* 链接样式优化 */
a {
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-bottom-color 0.2s;
}

a:hover {
  border-bottom-color: var(--md-accent-fg-color);
}

/* 博客文章列表样式 */
.md-content article {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--md-default-fg-color--lightest);
}

.md-content article:last-child {
  border-bottom: none;
}

/* 文章元信息样式 */
.post-meta {
  color: var(--md-default-fg-color--light);
  font-size: 0.9em;
  margin-bottom: 0.5rem;
}

.post-meta .post-date {
  margin-right: 1rem;
}

.post-meta .post-category {
  margin-right: 0.5rem;
}

.post-meta .post-tags a {
  background: var(--md-accent-fg-color--transparent);
  color: var(--md-accent-fg-color);
  padding: 0.2rem 0.5rem;
  border-radius: 0.2rem;
  font-size: 0.8em;
  margin-right: 0.3rem;
  text-decoration: none;
}

/* 精选文章卡片样式 */
.featured-posts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.featured-post {
  background: var(--md-default-bg-color);
  border: 1px solid var(--md-default-fg-color--lightest);
  border-radius: 0.5rem;
  padding: 1.5rem;
  transition: box-shadow 0.2s, transform 0.2s;
}

.featured-post:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.featured-post h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

.featured-post .post-excerpt {
  color: var(--md-default-fg-color--light);
  margin-bottom: 1rem;
}

.featured-post .read-more {
  color: var(--md-accent-fg-color);
  font-weight: 500;
  text-decoration: none;
}

/* 导航卡片样式 */
.nav-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
}

.nav-card {
  background: var(--md-default-bg-color);
  border: 1px solid var(--md-default-fg-color--lightest);
  border-radius: 0.5rem;
  padding: 1.5rem;
  text-align: center;
  transition: box-shadow 0.2s, transform 0.2s;
  text-decoration: none;
  color: inherit;
}

.nav-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
  text-decoration: none;
  color: inherit;
}

.nav-card .icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--md-accent-fg-color);
}

.nav-card h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
}

.nav-card p {
  margin: 0;
  color: var(--md-default-fg-color--light);
  font-size: 0.9rem;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  body {
    font-size: 15px;
  }
  
  .featured-posts {
    grid-template-columns: 1fr;
  }
  
  .nav-cards {
    grid-template-columns: 1fr;
  }
}

/* 目录样式优化 */
.md-nav--secondary .md-nav__title {
  font-weight: 600;
  color: var(--md-default-fg-color);
}

.md-nav--secondary .md-nav__link {
  font-size: 0.9rem;
  line-height: 1.5;
}

/* 搜索结果优化 */
.md-search-result__meta {
  color: var(--md-default-fg-color--light);
  font-size: 0.8rem;
}

/* 标签页面样式 */
.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 1rem 0;
}

.tag-cloud a {
  background: var(--md-accent-fg-color--transparent);
  color: var(--md-accent-fg-color);
  padding: 0.3rem 0.8rem;
  border-radius: 1rem;
  font-size: 0.9rem;
  text-decoration: none;
  transition: background-color 0.2s;
}

.tag-cloud a:hover {
  background: var(--md-accent-fg-color);
  color: var(--md-primary-bg-color);
}

/* 博客页面特殊布局 */
.blog-sidebar-nav {
  position: sticky;
  top: 4rem;
  max-height: calc(100vh - 8rem);
  overflow-y: auto;
  background: var(--md-default-bg-color);
  border: 1px solid var(--md-default-fg-color--lightest);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 2rem;
}

.blog-sidebar-nav h3 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  color: var(--md-accent-fg-color);
  border-bottom: 1px solid var(--md-default-fg-color--lightest);
  padding-bottom: 0.5rem;
}

.blog-nav-section {
  margin-bottom: 1.5rem;
}

.blog-nav-section:last-child {
  margin-bottom: 0;
}

.blog-nav-toggle {
  display: none;
}

.blog-nav-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: var(--md-default-fg-color);
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--md-default-fg-color--lightest);
  margin-bottom: 0.5rem;
}

.blog-nav-label:hover {
  color: var(--md-accent-fg-color);
}

.blog-nav-icon {
  margin-right: 0.5rem;
  transition: transform 0.2s;
}

.blog-nav-toggle:checked + .blog-nav-label .blog-nav-icon {
  transform: rotate(90deg);
}

.blog-nav-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.blog-nav-toggle:checked + .blog-nav-label + .blog-nav-content {
  max-height: 500px;
}

.blog-nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.blog-nav-list li {
  margin-bottom: 0.3rem;
}

.blog-nav-list a {
  display: block;
  padding: 0.3rem 0.5rem;
  color: var(--md-default-fg-color--light);
  text-decoration: none;
  border-radius: 0.2rem;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.blog-nav-list a:hover {
  background: var(--md-accent-fg-color--transparent);
  color: var(--md-accent-fg-color);
}

.blog-nav-list .nav-count {
  float: right;
  font-size: 0.8rem;
  color: var(--md-default-fg-color--lighter);
}

/* 博客文章样式增强 */
.blog-post-item {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--md-default-bg-color);
  border: 1px solid var(--md-default-fg-color--lightest);
  border-radius: 0.5rem;
  transition: box-shadow 0.2s;
}

.blog-post-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.blog-post-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: var(--md-default-fg-color--light);
}

.blog-post-meta .meta-item {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.blog-post-categories a,
.blog-post-tags a {
  background: var(--md-accent-fg-color--transparent);
  color: var(--md-accent-fg-color);
  padding: 0.2rem 0.5rem;
  border-radius: 0.2rem;
  text-decoration: none;
  font-size: 0.8rem;
  margin-right: 0.3rem;
}

.blog-post-excerpt {
  color: var(--md-default-fg-color--light);
  line-height: 1.6;
  margin-bottom: 1rem;
}

/* 响应式博客布局 */
@media screen and (min-width: 1200px) {
  .md-content__inner {
    max-width: none;
  }

  .blog-layout {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
  }

  .blog-main-content {
    min-width: 0;
  }
}

@media screen and (max-width: 1199px) {
  .blog-sidebar-nav {
    position: static;
    max-height: none;
  }
}

window.MathJax = {
  tex: {
    inlineMath: [["\\(", "\\)"]],
    displayMath: [["\\[", "\\]"]],
    processEscapes: true,
    processEnvironments: true
  },
  options: {
    ignoreHtmlClass: ".*|",
    processHtmlClass: "arithmatex"
  }
};

document$.subscribe(() => {
  MathJax.typesetPromise()
});

// 博客侧边栏交互功能
document.addEventListener('DOMContentLoaded', function() {
  // 初始化折叠状态
  const toggles = document.querySelectorAll('.blog-nav-toggle');

  toggles.forEach(toggle => {
    // 设置默认展开状态
    if (toggle.id.includes('toggle')) {
      toggle.checked = true;
    }

    // 添加点击事件监听
    toggle.addEventListener('change', function() {
      const content = this.nextElementSibling.nextElementSibling;
      if (content && content.classList.contains('blog-nav-content')) {
        if (this.checked) {
          content.style.maxHeight = content.scrollHeight + 'px';
        } else {
          content.style.maxHeight = '0';
        }
      }
    });

    // 初始化展开状态
    if (toggle.checked) {
      const content = toggle.nextElementSibling.nextElementSibling;
      if (content && content.classList.contains('blog-nav-content')) {
        content.style.maxHeight = content.scrollHeight + 'px';
      }
    }
  });

  // 平滑滚动到锚点
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });
});

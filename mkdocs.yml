site_name: NeXtep's Blog
site_url: https://InTheFuture7.github.io/
site_author: NeXtep
site_description: NeXtep的个人博客，分享技术学习和生活感悟

theme:
    name: material
    custom_dir: docs/overrides
    language: zh

    palette:
        - media: "(prefers-color-scheme)"
          toggle:
            icon: material/brightness-auto
            name: 关闭自动模式
        - media: "(prefers-color-scheme: light)"
          scheme: default
          primary: blue grey
          accent: blue
          toggle:
            icon: material/brightness-7
            name: 切换至夜间模式
        - media: "(prefers-color-scheme: dark)"
          scheme: slate
          primary: black
          accent: indigo
          toggle:
            icon: material/brightness-4
            name: 切换至日间模式

    features:
        - navigation.tabs
        - navigation.sections
        - navigation.top
        - navigation.tracking
        - navigation.indexes
        - toc.follow
        - toc.integrate
        - search.suggest
        - search.highlight
        - search.share
        - content.tabs.link
        - content.code.annotation
        - content.code.copy
        - content.action.edit
        - content.action.view


plugins:
    - search:
        lang: zh
    - blog:
        blog_dir: blog
        blog_toc: true
        post_date_format: yyyy/MM/dd
        post_url_date_format: yyyy/MM/dd
        post_url_format: "{date}/{slug}"
        archive_toc: true
        categories_toc: true
        pagination_per_page: 10
        draft: true
        draft_if_future_date: true
    - tags:
        tags_file: tag/index.md

extra:
    social:
        - icon: fontawesome/brands/github
          link: https://github.com/InTheFuture7
          name: GitHub
    generator: false

markdown_extensions:
    - pymdownx.highlight:
        anchor_linenums: true
        line_spans: __span
        pygments_lang_class: true
    - pymdownx.inlinehilite
    - pymdownx.snippets
    - admonition
    - pymdownx.arithmatex:
        generic: true
    - footnotes
    - pymdownx.details
    - pymdownx.superfences:
        custom_fences:
          - name: mermaid
            class: mermaid
            format: !!python/name:pymdownx.superfences.fence_code_format
    - pymdownx.mark
    - pymdownx.caret
    - pymdownx.keys
    - pymdownx.tilde
    - attr_list
    - md_in_html
    - pymdownx.emoji
    - pymdownx.tabbed:
        alternate_style: true
    - pymdownx.tasklist:
        custom_checkbox: true
    - def_list
    - abbr


nav:
    - 首页: index.md
    - 博客:
        - blog/index.md
        - 机器学习数学基础:
            - 线性代数: blog/math4ml/linear_algebra.md
            - 概率论: blog/math4ml/probability_theory.md
            - 统计学: blog/math4ml/statistics.md
        - 深度学习:
            - 基础知识: blog/deepl/basic.md
        - 前沿论文:
            - blog/papers/index.md
    - 代码: code/index.md
    - 标签: tag/index.md
    - 工具: tool/index.md
    - 关于: about/index.md

extra_css:
    - stylesheets/extra.css

extra_javascript:
    - javascripts/mathjax.js
    - https://polyfill.io/v3/polyfill.min.js?features=es6
    - https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js

copyright: |
    &copy; 2025 <a href="https://github.com/InTheFuture7" target="_blank" rel="noopener">NeXtep</a>

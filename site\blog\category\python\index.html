
<!doctype html>
<html lang="zh" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="NeXtep的个人博客，分享技术学习和生活感悟">
      
      
        <meta name="author" content="NeXtep">
      
      
        <link rel="canonical" href="https://InTheFuture7.github.io/blog/category/python/">
      
      
        <link rel="prev" href="../../">
      
      
        <link rel="next" href="../%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0/">
      
      
      <link rel="icon" href="../../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.5.49">
    
    
      
        <title>Python - NeXtep's Blog</title>
      
    
    
      <link rel="stylesheet" href="../../../assets/stylesheets/main.6f8fc17f.min.css">
      
        
        <link rel="stylesheet" href="../../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
      <link rel="stylesheet" href="../../../stylesheets/extra.css">
    
    <script>__md_scope=new URL("../../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
      
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="indigo" data-md-color-accent="indigo">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#python" class="md-skip">
          跳转至
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

<header class="md-header" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="页眉">
    <a href="../../.." title="NeXtep&#39;s Blog" class="md-header__button md-logo" aria-label="NeXtep's Blog" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            NeXtep's Blog
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Python
            
          </span>
        </div>
      </div>
    </div>
    
      
        <form class="md-header__option" data-md-component="palette">
  
    
    
    
    <input class="md-option" data-md-color-media="(prefers-color-scheme)" data-md-color-scheme="default" data-md-color-primary="indigo" data-md-color-accent="indigo"  aria-label="关闭自动模式"  type="radio" name="__palette" id="__palette_0">
    
      <label class="md-header__button md-icon" title="关闭自动模式" for="__palette_1" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="m14.3 16-.7-2h-3.2l-.7 2H7.8L11 7h2l3.2 9zM20 8.69V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12zm-9.15 3.96h2.3L12 9z"/></svg>
      </label>
    
  
    
    
    
    <input class="md-option" data-md-color-media="(prefers-color-scheme: light)" data-md-color-scheme="default" data-md-color-primary="blue-grey" data-md-color-accent="blue"  aria-label="切换至夜间模式"  type="radio" name="__palette" id="__palette_1">
    
      <label class="md-header__button md-icon" title="切换至夜间模式" for="__palette_2" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
    
    
    
    <input class="md-option" data-md-color-media="(prefers-color-scheme: dark)" data-md-color-scheme="slate" data-md-color-primary="black" data-md-color-accent="indigo"  aria-label="切换至日间模式"  type="radio" name="__palette" id="__palette_2">
    
      <label class="md-header__button md-icon" title="切换至日间模式" for="__palette_0" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
</form>
      
    
    
      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
    
    
    
      <label class="md-header__button md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
      </label>
      <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="搜索" placeholder="搜索" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="查找">
        
          <a href="javascript:void(0)" class="md-search__icon md-icon" title="分享" aria-label="分享" data-clipboard data-clipboard-text="" data-md-component="search-share" tabindex="-1">
            
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.15c-.05.21-.08.43-.08.66 0 1.61 1.31 2.91 2.92 2.91s2.92-1.3 2.92-2.91A2.92 2.92 0 0 0 18 16.08"/></svg>
          </a>
        
        <button type="reset" class="md-search__icon md-icon" title="清空当前内容" aria-label="清空当前内容" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
        <div class="md-search__suggest" data-md-component="search-suggest"></div>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            正在初始化搜索引擎
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
            
<nav class="md-tabs" aria-label="标签" data-md-component="tabs">
  <div class="md-grid">
    <ul class="md-tabs__list">
      
        
  
  
  
    <li class="md-tabs__item">
      <a href="../../.." class="md-tabs__link">
        
  
    
  
  首页

      </a>
    </li>
  

      
        
  
  
    
  
  
    
    
      <li class="md-tabs__item md-tabs__item--active">
        <a href="../../" class="md-tabs__link">
          
  
    
  
  博客

        </a>
      </li>
    
  

      
        
  
  
  
    <li class="md-tabs__item">
      <a href="../../../code/" class="md-tabs__link">
        
  
    
  
  代码

      </a>
    </li>
  

      
        
  
  
  
    <li class="md-tabs__item">
      <a href="../../../tag/" class="md-tabs__link">
        
  
    
  
  标签

      </a>
    </li>
  

      
        
  
  
  
    <li class="md-tabs__item">
      <a href="../../../tool/" class="md-tabs__link">
        
  
    
  
  工具

      </a>
    </li>
  

      
        
  
  
  
    <li class="md-tabs__item">
      <a href="../../../about/" class="md-tabs__link">
        
  
    
  
  关于

      </a>
    </li>
  

      
    </ul>
  </div>
</nav>
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    


  


  

<nav class="md-nav md-nav--primary md-nav--lifted md-nav--integrated" aria-label="导航栏" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../../.." title="NeXtep&#39;s Blog" class="md-nav__button md-logo" aria-label="NeXtep's Blog" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    NeXtep's Blog
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../.." class="md-nav__link">
        
  
  <span class="md-ellipsis">
    首页
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
      
        
          
        
      
        
      
        
      
        
      
        
      
        
      
    
    
      
        
        
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_2" checked>
        
          
          
          <div class="md-nav__link md-nav__container">
            <a href="../../" class="md-nav__link ">
              
  
  <span class="md-ellipsis">
    博客
  </span>
  

            </a>
            
              
              <label class="md-nav__link " for="__nav_2" id="__nav_2_label" tabindex="">
                <span class="md-nav__icon md-icon"></span>
              </label>
            
          </div>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            博客
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
            
              
                
  
  
  
  
    
    
      
        
      
        
      
        
      
    
    
      
      
        
          
          
        
      
    
    
    <li class="md-nav__item md-nav__item--section md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_2_2" >
        
          
          <label class="md-nav__link" for="__nav_2_2" id="__nav_2_2_label" tabindex="">
            
  
  <span class="md-ellipsis">
    机器学习数学基础
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_2_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2_2">
            <span class="md-nav__icon md-icon"></span>
            机器学习数学基础
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../math4ml/linear_algebra/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    线性代数
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../math4ml/probability_theory/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    概率论
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../math4ml/statistics/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    统计学
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    
    
      
        
      
    
    
      
      
        
          
          
        
      
    
    
    <li class="md-nav__item md-nav__item--section md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_2_3" >
        
          
          <label class="md-nav__link" for="__nav_2_3" id="__nav_2_3_label" tabindex="">
            
  
  <span class="md-ellipsis">
    深度学习
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_2_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2_3">
            <span class="md-nav__icon md-icon"></span>
            深度学习
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../deepl/basic/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    基础知识
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    
    
      
        
          
        
      
    
    
      
      
        
          
          
        
      
    
    
    <li class="md-nav__item md-nav__item--section md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_2_4" >
        
          
          
          <div class="md-nav__link md-nav__container">
            <a href="../../papers/" class="md-nav__link ">
              
  
  <span class="md-ellipsis">
    前沿论文
  </span>
  

            </a>
            
          </div>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_2_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2_4">
            <span class="md-nav__icon md-icon"></span>
            前沿论文
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
            
          </ul>
        </nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    
    
      
        
      
    
    
      
      
        
          
          
        
      
    
    
    <li class="md-nav__item md-nav__item--section md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_2_5" >
        
          
          <label class="md-nav__link" for="__nav_2_5" id="__nav_2_5_label" tabindex="">
            
  
  <span class="md-ellipsis">
    归档
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_2_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2_5">
            <span class="md-nav__icon md-icon"></span>
            归档
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
    
  
  
    <li class="md-nav__item">
      <a href="../../archive/2025/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    2025
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
              
                
  
  
    
  
  
  
    
    
      
        
      
        
      
        
      
        
      
    
    
      
      
        
          
          
        
      
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_2_6" checked>
        
          
          <label class="md-nav__link" for="__nav_2_6" id="__nav_2_6_label" tabindex="">
            
  
  <span class="md-ellipsis">
    分类
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_2_6_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_2_6">
            <span class="md-nav__icon md-icon"></span>
            分类
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
    
  
  
    
      
    
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  <span class="md-ellipsis">
    Python
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  <span class="md-ellipsis">
    Python
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="目录">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      目录
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#_1" class="md-nav__link">
    <span class="md-ellipsis">
      我的第一篇博客文章
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
    
  
  
    <li class="md-nav__item">
      <a href="../%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    机器学习
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
    
  
  
    <li class="md-nav__item">
      <a href="../%E6%B7%B1%E5%BA%A6%E5%AD%A6%E4%B9%A0/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    深度学习
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
    
  
  
    <li class="md-nav__item">
      <a href="../%E7%A5%9E%E7%BB%8F%E7%BD%91%E7%BB%9C/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    神经网络
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../code/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    代码
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../tag/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    标签
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../tool/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    工具
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../../about/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    关于
  </span>
  

      </a>
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
<div class="md-content" data-md-component="content">
  <article class="md-content__inner md-typeset">
    
    <!-- 博客页面标题 -->
    <header class="md-typeset">
      <h1>Python</h1>
      
    </header>

    <!-- 博客内容 -->
    <div class="blog-layout">
      <!-- 左侧导航 -->
      <aside class="blog-sidebar">
        <!-- 博客页面的自定义导航 -->
<nav class="md-nav md-nav--secondary" aria-label="博客导航">
  <label class="md-nav__title" for="__nav_blog">
    <span class="md-nav__icon md-icon"></span>
    博客导航
  </label>
  <input class="md-nav__toggle md-toggle" type="checkbox" id="__nav_blog" checked>
  <ul class="md-nav__list">
    
    <!-- 归档部分 -->
    <li class="md-nav__item md-nav__item--nested">
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__nav_archive" checked>
      <label class="md-nav__link" for="__nav_archive">
        <span class="md-nav__icon md-icon"></span>
        📅 归档
      </label>
      <nav class="md-nav" aria-label="归档">
        <ul class="md-nav__list">
          <li class="md-nav__item md-nav__item--nested">
            <input class="md-nav__toggle md-toggle" type="checkbox" id="__nav_2025">
            <label class="md-nav__link" for="__nav_2025">
              <span class="md-nav__icon md-icon"></span>
              2025年
            </label>
            <nav class="md-nav" aria-label="2025年">
              <ul class="md-nav__list">
                <li class="md-nav__item">
                  <a href="/blog/2025/01/" class="md-nav__link">
                    1月 (3篇)
                  </a>
                </li>
              </ul>
            </nav>
          </li>
          <li class="md-nav__item md-nav__item--nested">
            <input class="md-nav__toggle md-toggle" type="checkbox" id="__nav_2024">
            <label class="md-nav__link" for="__nav_2024">
              <span class="md-nav__icon md-icon"></span>
              2024年
            </label>
            <nav class="md-nav" aria-label="2024年">
              <ul class="md-nav__list">
                <li class="md-nav__item">
                  <a href="/blog/2024/12/" class="md-nav__link">
                    12月 (0篇)
                  </a>
                </li>
              </ul>
            </nav>
          </li>
        </ul>
      </nav>
    </li>

    <!-- 标签部分 -->
    <li class="md-nav__item md-nav__item--nested">
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__nav_tags" checked>
      <label class="md-nav__link" for="__nav_tags">
        <span class="md-nav__icon md-icon"></span>
        🏷️ 标签
      </label>
      <nav class="md-nav" aria-label="标签">
        <ul class="md-nav__list">
          <li class="md-nav__item md-nav__item--nested">
            <input class="md-nav__toggle md-toggle" type="checkbox" id="__nav_tech_tags">
            <label class="md-nav__link" for="__nav_tech_tags">
              <span class="md-nav__icon md-icon"></span>
              技术分类
            </label>
            <nav class="md-nav" aria-label="技术分类">
              <ul class="md-nav__list">
                <li class="md-nav__item">
                  <a href="/blog/tags/#机器学习" class="md-nav__link">
                    机器学习 (2篇)
                  </a>
                </li>
                <li class="md-nav__item">
                  <a href="/blog/tags/#深度学习" class="md-nav__link">
                    深度学习 (2篇)
                  </a>
                </li>
                <li class="md-nav__item">
                  <a href="/blog/tags/#Python" class="md-nav__link">
                    Python (2篇)
                  </a>
                </li>
                <li class="md-nav__item">
                  <a href="/blog/tags/#PyTorch" class="md-nav__link">
                    PyTorch (1篇)
                  </a>
                </li>
                <li class="md-nav__item">
                  <a href="/blog/tags/#神经网络" class="md-nav__link">
                    神经网络 (1篇)
                  </a>
                </li>
              </ul>
            </nav>
          </li>
          <li class="md-nav__item md-nav__item--nested">
            <input class="md-nav__toggle md-toggle" type="checkbox" id="__nav_level_tags">
            <label class="md-nav__link" for="__nav_level_tags">
              <span class="md-nav__icon md-icon"></span>
              难度分类
            </label>
            <nav class="md-nav" aria-label="难度分类">
              <ul class="md-nav__list">
                <li class="md-nav__item">
                  <a href="/blog/tags/#入门" class="md-nav__link">
                    入门 (2篇)
                  </a>
                </li>
                <li class="md-nav__item">
                  <a href="/blog/tags/#教程" class="md-nav__link">
                    教程 (2篇)
                  </a>
                </li>
                <li class="md-nav__item">
                  <a href="/blog/tags/#理论" class="md-nav__link">
                    理论 (1篇)
                  </a>
                </li>
                <li class="md-nav__item">
                  <a href="/blog/tags/#算法" class="md-nav__link">
                    算法 (1篇)
                  </a>
                </li>
              </ul>
            </nav>
          </li>
        </ul>
      </nav>
    </li>

    <!-- 分类部分 -->
    <li class="md-nav__item md-nav__item--nested">
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__nav_categories">
      <label class="md-nav__link" for="__nav_categories">
        <span class="md-nav__icon md-icon"></span>
        📂 分类
      </label>
      <nav class="md-nav" aria-label="分类">
        <ul class="md-nav__list">
          <li class="md-nav__item">
            <a href="/blog/category/机器学习/" class="md-nav__link">
              机器学习 (2篇)
            </a>
          </li>
          <li class="md-nav__item">
            <a href="/blog/category/深度学习/" class="md-nav__link">
              深度学习 (2篇)
            </a>
          </li>
          <li class="md-nav__item">
            <a href="/blog/category/神经网络/" class="md-nav__link">
              神经网络 (1篇)
            </a>
          </li>
        </ul>
      </nav>
    </li>

  </ul>
</nav>

<style>
/* 自定义博客导航样式 */
.md-nav--secondary .md-nav__title {
  font-weight: 600;
  color: var(--md-default-fg-color);
  padding: 0.6rem 1.2rem;
  background: var(--md-default-fg-color--lightest);
  border-radius: 0.2rem;
  margin-bottom: 0.5rem;
}

.md-nav--secondary .md-nav__item--nested > .md-nav__link {
  font-weight: 500;
  color: var(--md-accent-fg-color);
}

.md-nav--secondary .md-nav__link:hover {
  background: var(--md-accent-fg-color--transparent);
}

.md-nav--secondary .md-nav__list .md-nav__list {
  padding-left: 1rem;
}

.md-nav--secondary .md-nav__link {
  font-size: 0.9rem;
  line-height: 1.4;
  padding: 0.3rem 0.8rem;
  border-radius: 0.2rem;
  transition: background-color 0.2s;
}
</style>
      </aside>

      <!-- 主要内容区域 -->
      <main class="blog-content">
        <h1 id="python">Python</h1>
        
        <!-- 博客文章列表 -->
        
      </main>

      <!-- 右侧目录 -->
      <aside class="blog-toc">
        
          <nav class="md-nav md-nav--secondary" aria-label="目录">
            <label class="md-nav__title" for="__toc">
              <span class="md-nav__icon md-icon"></span>
              目录
            </label>
            <ul class="md-nav__list">
              
                <li class="md-nav__item">
                  <a href="#python" class="md-nav__link">
                    Python
                  </a>
                  
                    <ul class="md-nav__list">
                      
                        <li class="md-nav__item">
                          <a href="#_1" class="md-nav__link">
                            我的第一篇博客文章
                          </a>
                        </li>
                      
                    </ul>
                  
                </li>
              
            </ul>
          </nav>
        
      </aside>
    </div>

  </article>
</div>

<style>
/* 博客布局样式 */
.blog-layout {
  display: grid;
  grid-template-columns: 250px 1fr 200px;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.blog-sidebar {
  position: sticky;
  top: 4rem;
  height: fit-content;
  max-height: calc(100vh - 8rem);
  overflow-y: auto;
}

.blog-content {
  min-width: 0; /* 防止内容溢出 */
}

.blog-toc {
  position: sticky;
  top: 4rem;
  height: fit-content;
  max-height: calc(100vh - 8rem);
  overflow-y: auto;
}

/* 博客文章预览样式 */
.blog-posts {
  margin-top: 2rem;
}

.blog-post-preview {
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid var(--md-default-fg-color--lightest);
}

.blog-post-preview:last-child {
  border-bottom: none;
}

.post-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.post-header h2 a {
  color: var(--md-default-fg-color);
  text-decoration: none;
}

.post-header h2 a:hover {
  color: var(--md-accent-fg-color);
}

.post-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: var(--md-default-fg-color--light);
}

.post-category {
  background: var(--md-accent-fg-color--transparent);
  color: var(--md-accent-fg-color);
  padding: 0.2rem 0.5rem;
  border-radius: 0.2rem;
  text-decoration: none;
  font-size: 0.8rem;
}

.post-tag {
  color: var(--md-accent-fg-color);
  text-decoration: none;
  font-size: 0.8rem;
}

.post-tag:hover {
  text-decoration: underline;
}

.post-excerpt {
  color: var(--md-default-fg-color--light);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.read-more {
  color: var(--md-accent-fg-color);
  font-weight: 500;
  text-decoration: none;
}

.read-more:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media screen and (max-width: 1200px) {
  .blog-layout {
    grid-template-columns: 200px 1fr 180px;
    gap: 1.5rem;
  }
}

@media screen and (max-width: 960px) {
  .blog-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .blog-sidebar,
  .blog-toc {
    position: static;
    max-height: none;
  }
  
  .blog-sidebar {
    order: 2;
  }
  
  .blog-content {
    order: 1;
  }
  
  .blog-toc {
    order: 3;
  }
}

@media screen and (max-width: 768px) {
  .post-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>

              </article>
            </div>
          
          
  <script>var tabs=__md_get("__tabs");if(Array.isArray(tabs))e:for(var set of document.querySelectorAll(".tabbed-set")){var labels=set.querySelector(".tabbed-labels");for(var tab of tabs)for(var label of labels.getElementsByTagName("label"))if(label.innerText.trim()===tab){var input=document.getElementById(label.htmlFor);input.checked=!0;continue e}}</script>

<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
          <button type="button" class="md-top md-icon" data-md-component="top" hidden>
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  回到页面顶部
</button>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
    <div class="md-copyright__highlight">
      &copy; 2025 <a href="https://github.com/InTheFuture7" target="_blank" rel="noopener">NeXtep</a>

    </div>
  
  
</div>
      
        <div class="md-social">
  
    
    
    
    
    <a href="https://github.com/InTheFuture7" target="_blank" rel="noopener" title="GitHub" class="md-social__link">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 496 512"><!--! Font Awesome Free 6.7.1 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2024 Fonticons, Inc.--><path d="M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8M97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    <script id="__config" type="application/json">{"base": "../../..", "features": ["navigation.tabs", "navigation.sections", "navigation.top", "navigation.tracking", "navigation.indexes", "toc.follow", "toc.integrate", "search.suggest", "search.highlight", "search.share", "content.tabs.link", "content.code.annotation", "content.code.copy", "content.action.edit", "content.action.view"], "search": "../../../assets/javascripts/workers/search.6ce7567c.min.js", "translations": {"clipboard.copied": "\u5df2\u590d\u5236", "clipboard.copy": "\u590d\u5236", "search.result.more.one": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.more.other": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 # \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.none": "\u6ca1\u6709\u627e\u5230\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.one": "\u627e\u5230 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.other": "# \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.placeholder": "\u952e\u5165\u4ee5\u5f00\u59cb\u641c\u7d22", "search.result.term.missing": "\u7f3a\u5c11", "select.version": "\u9009\u62e9\u5f53\u524d\u7248\u672c"}}</script>
    
    
      <script src="../../../assets/javascripts/bundle.88dd0f4e.min.js"></script>
      
        <script src="../../../javascripts/mathjax.js"></script>
      
        <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
      
        <script src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
      
    
  </body>
</html>